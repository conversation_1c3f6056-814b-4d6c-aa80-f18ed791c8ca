﻿<?xml version="1.0" encoding="utf-8"?>
<skin>
	<imglist name="skin_main_icon" src="IMG:icon_main" states="1" />
  <imglist name="skin_logo" src="img:png_logo" filterLevel="high"/>
	<imglist name="skin_open_menu_png" src="IMG:open_menu_png" states="4" />
	<scrollbar name="skin_bb_scrollbar" src="img:default.scrollbar" margin="2" />
	<imglist name="skin_caption_line" src="img:caption_line" />
	<imgframe name="skin_app_button" src="img:png_tool_button" states="3" margin="5,5,5,5"/>
	<imglist name="skin_prop_switch" src="img:png_prop_switch" states="4" />
	<imglist name="skin_tab_skin" src="img:png_tab_skin" states="3" />
	<imglist name="skin_blue_button" src="img:png_btn_blue" states="3" />
	<imglist name="skin_app_button2" src="img:png_app_button" states="3" />
	<imglist name="skin_tree_toggle_png" src="IMG:tree_toggle_png" states="6" />
	<imglist name="skin_menu_skin_png" src="IMG:menu_skin_png" states="2" />
	<imglist name="skin_menu_sep_png" src="IMG:menu_sep_png" states="1" />
  <imgframe name="skin_toolbar_state" src="img:png_toolbar_state" states="3" margin="5,5,5,5"/>
  <imglist name="skin_toolbar_prop" src="img:png_toolbar_prop" states="2"/>
  <imglist name="skin_help" src="img:png_btn_help" states="3"/>
  <imglist name="skin_designer_background" src="img:png_designer_background" tile="1"/>
  <imglist name="skin_filetypes" src="img:png_filetypes" states="12" />
  <imgframe name="skin_item_bk" src="img:png_item_bk" states="3" margin="3,3,3,3" />
  <colorrect name="skin_item_bk2" normal="rgba(255,255,255,10)" hover="rgba(255,0,0,128)" pushdown="rgba(0,255,255,128)"/>
</skin>
